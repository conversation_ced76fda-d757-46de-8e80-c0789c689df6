using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.IdentityModel.Tokens.Jwt;
using TeyaMobile.Shared.Services;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobile.Services
{
    /// <summary>
    /// Simplified Android authentication service using MSAL
    /// </summary>
    public class SimpleAndroidAuthenticationService : IAuthenticationService
    {
        private readonly IPublicClientApplication _publicClientApp;
        private readonly ILogger<SimpleAndroidAuthenticationService> _logger;
        private readonly IConfiguration _configuration;

        private string[] _scopes;

        public SimpleAndroidAuthenticationService(
            IPublicClientApplication publicClientApp,
            ILogger<SimpleAndroidAuthenticationService> logger,
            IConfiguration configuration)
        {
            _publicClientApp = publicClientApp;
            _logger = logger;
            _configuration = configuration;
            
            // Get scopes from configuration
            var azureAdSection = _configuration.GetSection("AzureAd");
            var scopesConfig = azureAdSection.GetSection("Scopes").Get<string[]>();
            _scopes = scopesConfig ?? new[] { "openid" };
        }

        public bool IsAuthenticated
        {
            get
            {
                try
                {
                    var accounts = _publicClientApp.GetAccountsAsync().Result;
                    return accounts.Any();
                }
                catch
                {
                    return false;
                }
            }
        }

        public async Task<bool> LoginAsync()
        {
            try
            {
                // Try silent authentication first
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (accounts.Any())
                {
                    try
                    {
                        var result = await _publicClientApp.AcquireTokenSilent(_scopes, accounts.FirstOrDefault())
                            .ExecuteAsync();
                        return true;
                    }
                    catch (MsalUiRequiredException)
                    {
                        // Silent auth failed, need interactive auth
                    }
                }

                // Interactive authentication
                var authResult = await _publicClientApp.AcquireTokenInteractive(_scopes)
                    .WithPrompt(Prompt.SelectAccount)
                    .ExecuteAsync();

                return authResult != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Login failed: {Error}", ex.Message);
                return false;
            }
        }

        public async Task LogoutAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                foreach (var account in accounts)
                {
                    await _publicClientApp.RemoveAsync(account);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Logout failed: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<string> GetAccessTokenAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (!accounts.Any())
                    return string.Empty;

                // Use Graph API scope by default
                var graphScopes = new[] { "https://graph.microsoft.com/.default" };
                var result = await _publicClientApp.AcquireTokenSilent(graphScopes, accounts.FirstOrDefault())
                    .ExecuteAsync();

                _logger.LogInformation("Successfully acquired Graph API token for Android");
                return result.AccessToken;
            }
            catch (MsalUiRequiredException)
            {
                try
                {
                    // If silent acquisition fails, try interactive
                    var graphScopes = new[] { "https://graph.microsoft.com/.default" };
                    var result = await _publicClientApp.AcquireTokenInteractive(graphScopes)
                        .WithPrompt(Prompt.SelectAccount)
                        .ExecuteAsync();

                    _logger.LogInformation("Successfully acquired Graph API token interactively for Android");
                    return result.AccessToken;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to get Graph API access token interactively: {Error}", ex.Message);
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Graph API access token: {Error}", ex.Message);
                return string.Empty;
            }
        }

        public async Task<string> GetUserNameAsync()
        {
            try
            {
                var accessToken = await GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                    return string.Empty;

                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadJwtToken(accessToken);

                return jsonToken.Claims.FirstOrDefault(c => c.Type == "name")?.Value ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user name: {Error}", ex.Message);
                return string.Empty;
            }
        }

        public async Task<string> GetUserEmailAsync()
        {
            try
            {
                var accessToken = await GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                    return string.Empty;

                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadJwtToken(accessToken);

                return jsonToken.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ??
                       jsonToken.Claims.FirstOrDefault(c => c.Type == "email")?.Value ??
                       string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user email: {Error}", ex.Message);
                return string.Empty;
            }
        }

        public async Task<string> GetGraphApiScopeAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (!accounts.Any())
                    return string.Empty;

                // Get Graph API scope from configuration
                var graphScope = _configuration["Graph_Auth_Scope"] ?? "https://graph.microsoft.com/.default";
                var graphScopes = new[] { graphScope };

                var result = await _publicClientApp.AcquireTokenSilent(graphScopes, accounts.FirstOrDefault())
                    .ExecuteAsync();

                return result.AccessToken;
            }
            catch (MsalUiRequiredException)
            {
                try
                {
                    // If silent acquisition fails, try interactive
                    var graphScope = _configuration["Graph_Auth_Scope"] ?? "https://graph.microsoft.com/.default";
                    var graphScopes = new[] { graphScope };

                    var result = await _publicClientApp.AcquireTokenInteractive(graphScopes)
                        .WithPrompt(Prompt.SelectAccount)
                        .ExecuteAsync();

                    return result.AccessToken;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to get Graph API access token interactively: {Error}", ex.Message);
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Graph API access token: {Error}", ex.Message);
                return string.Empty;
            }
        }

        public async Task<string> GetServiceSpecificTokenAsync()
        {
            try
            {
                // Use client credentials flow for service-specific API access
                var authority = Environment.GetEnvironmentVariable("AUTH_AUTHORITY");
                var clientId = Environment.GetEnvironmentVariable("AUTH_CLIENT_ID");
                var clientSecret = Environment.GetEnvironmentVariable("AUTH_CLIENT_SECRET");

                if (string.IsNullOrEmpty(authority) || string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret))
                {
                    _logger.LogWarning("AUTH credentials not configured for client credentials flow");
                    return string.Empty;
                }

                // Use the CIAM authority for token endpoint
                var tokenUrl = $"{authority.TrimEnd('/')}/oauth2/v2.0/token";
                var httpClient = new HttpClient();

                var content = new FormUrlEncodedContent(new[]
                {
                    new KeyValuePair<string, string>("grant_type", "client_credentials"),
                    new KeyValuePair<string, string>("client_id", clientId),
                    new KeyValuePair<string, string>("client_secret", clientSecret),
                    new KeyValuePair<string, string>("scope", "api://e21369d6-92b3-446b-b981-0291bcb29b1b/.default")
                });

                var response = await httpClient.PostAsync(tokenUrl, content);
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to acquire service-specific token: {StatusCode} - {Error}", response.StatusCode, errorContent);
                    return string.Empty;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var tokenResponse = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(responseContent);

                if (tokenResponse != null && tokenResponse.TryGetValue("access_token", out var accessToken))
                {
                    _logger.LogInformation("Successfully acquired service-specific token using client credentials flow for Android");
                    return accessToken.ToString() ?? string.Empty;
                }

                _logger.LogWarning("Failed to parse service-specific token response");
                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get service-specific access token: {Error}", ex.Message);
                return string.Empty;
            }
        }
    }
}
