using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Components;
using TeyaMobile.Shared.Services;
using TeyaMobileViewModel.ViewModel;
using System.Security.Claims;

namespace TeyaMobile.Web.Services
{
    /// <summary>
    /// Web authentication service compatible with TeyaWebApp approach
    /// </summary>
    public class TeyaWebAppAuthenticationService : TeyaMobileViewModel.ViewModel.IAuthenticationService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TeyaWebAppAuthenticationService> _logger;
        private readonly ITokenService _tokenService;

        public TeyaWebAppAuthenticationService(
            IHttpContextAccessor httpContextAccessor,
            IServiceProvider serviceProvider,
            ILogger<TeyaWebAppAuthenticationService> logger,
            ITokenService tokenService)
        {
            _httpContextAccessor = httpContextAccessor;
            _serviceProvider = serviceProvider;
            _logger = logger;
            _tokenService = tokenService;
        }

        public bool IsAuthenticated =>
            _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;

        public Task<bool> LoginAsync()
        {
            try
            {
                // Check if user is already authenticated
                if (IsAuthenticated)
                {
                    return Task.FromResult(true);
                }

                var navigationManager = _serviceProvider.GetRequiredService<NavigationManager>();

                // Trigger the authentication challenge using OpenID Connect
                navigationManager.NavigateTo("/authentication/login", forceLoad: true);

                // Return false since authentication is in progress via redirect
                return Task.FromResult(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Login failed: {Error}", ex.Message);
                return Task.FromResult(false);
            }
        }

        public Task LogoutAsync()
        {
            try
            {
                var navigationManager = _serviceProvider.GetRequiredService<NavigationManager>();

                // Clear tokens from TokenService
                _tokenService.AccessToken = null;
                _tokenService.AccessToken2 = null;
                _tokenService.UserDetails = null;

                // Trigger the logout using OpenID Connect
                navigationManager.NavigateTo("/authentication/logout", forceLoad: true);

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Logout failed: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<string> GetAccessTokenAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext == null || !IsAuthenticated)
                    return string.Empty;

                // Get token from TokenService (stored via TeyaWebApp OBO flow)
                var token = await _tokenService.GetValidatedAccessTokenAsync();
                if (!string.IsNullOrEmpty(token))
                    return token;

                // Fallback: Get access token from authentication properties
                var storedToken = await httpContext.GetTokenAsync("access_token");
                return storedToken ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get access token");
                return string.Empty;
            }
        }

        public Task<string> GetUserNameAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext?.User?.Identity?.IsAuthenticated != true)
                    return Task.FromResult(string.Empty);

                var claims = httpContext.User.Claims;
                var displayName = claims.FirstOrDefault(c => c.Type == "name")?.Value ??
                                 claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value ?? 
                                 string.Empty;

                return Task.FromResult(displayName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user name");
                return Task.FromResult(string.Empty);
            }
        }

        public Task<string> GetUserEmailAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext?.User?.Identity?.IsAuthenticated != true)
                    return Task.FromResult(string.Empty);

                var claims = httpContext.User.Claims;
                var email = claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value ??
                           claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ?? 
                           string.Empty;

                return Task.FromResult(email);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user email");
                return Task.FromResult(string.Empty);
            }
        }
    }
}
